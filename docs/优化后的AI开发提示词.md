# 宠物博客站群系统 - AI开发指导文档

## 🎯 项目概述与核心目标

### 项目背景
我需要开发一个专业的宠物博客站群系统，专注于猫狗相关的知识分享内容。该系统将服务于多个国家和语言市场（美国、德国、俄罗斯），每个市场使用独立的顶级域名和语言模板，通过统一的后台管理系统进行内容管理和发布。

### 商业目标
- 建立多语言宠物知识分享平台，获得Google搜索引擎的优质排名
- 通过Google广告实现商业变现
- 建立可扩展的站群架构，支持后续添加更多语言和市场
- 日均发布5篇高质量文章，预期前期访问量1万左右

### 核心价值主张
- **专业性**：专注猫狗宠物知识，内容垂直且专业
- **本地化**：每个语言市场独立运营，符合当地用户习惯
- **SEO优化**：严格按照Google SEO最佳实践设计
- **易管理**：统一后台管理多个站点，提高运营效率

## 🏗️ 技术架构要求

### 技术栈选择
- **前端框架**：Astro（静态站点生成，SEO友好）
- **后端框架**：Node.js + Express.js（成熟稳定，生态丰富）
- **数据库**：MySQL 9.0.1（关系型数据库，适合内容管理）
- **缓存系统**：Redis（提升性能，减少数据库压力）
- **部署环境**：Linux + 宝塔面板（用户熟悉的环境）

### 架构设计原则
1. **单一职责**：前端专注展示，后端专注数据处理
2. **数据隔离**：每个语言站点数据完全独立
3. **模块化设计**：功能模块可独立开发和测试
4. **可扩展性**：支持快速添加新语言和功能
5. **性能优先**：静态生成 + 缓存策略保证访问速度

### 系统架构图要求
请在文档中绘制详细的系统架构图，包括：
- 多域名请求流转路径
- 前后端交互流程
- 数据库设计结构
- 缓存策略布局
- 文件存储方案

## 📋 功能需求详述

### 前端功能模块

#### 必需页面清单
1. **首页（Homepage）**
   - 最新文章展示
   - 热门文章推荐
   - 分类导航
   - 搜索功能入口

2. **文章详情页（Article Page）**
   - 文章完整内容展示
   - 相关文章推荐
   - 评论系统（多层嵌套）
   - 社交分享功能

3. **分类页面（Category Page）**
   - 分类文章列表
   - 分页功能
   - 筛选和排序

4. **搜索结果页（Search Results）**
   - 关键词搜索结果
   - 搜索建议
   - 无结果页面处理

5. **静态页面**
   - 关于我们
   - 隐私政策
   - 使用条款
   - 联系我们

#### 前端技术要求
- **响应式设计**：完美适配桌面、平板、手机
- **加载速度**：首屏加载时间 < 3秒
- **SEO优化**：完整的meta标签、结构化数据、sitemap
- **用户体验**：直观的导航、清晰的内容层次
- **无障碍访问**：符合WCAG 2.1标准

### 后端功能模块

#### 内容管理系统
1. **文章管理**
   - 富文本编辑器（支持图片粘贴）
   - 草稿保存和发布
   - 文章状态管理（草稿/已发布/已下线）
   - 批量操作功能

2. **翻译工作流**
   - 原文（中文）录入
   - AI翻译接口调用
   - 翻译结果人工校对
   - 多语言版本管理

3. **评论管理**
   - 评论审核系统
   - 多层嵌套评论支持
   - 垃圾评论过滤
   - 批量审核操作

#### 站点管理系统
1. **域名语言绑定**
   - 域名与语言模板映射
   - 动态路由配置
   - 域名状态监控

2. **广告管理**
   - Google广告代码配置
   - 每个站点独立开关
   - 广告位置管理
   - 统计代码集成

3. **SEO设置**
   - 元数据模板配置
   - 结构化数据管理
   - Sitemap自动生成
   - robots.txt配置

## 🌍 多语言站群策略

### 核心策略说明
**重要**：严格执行"一语言一模板"策略，绝不使用i18n国际化方案。每种语言都是完全独立的前端模板，包括：
- 独立的HTML模板文件
- 独立的CSS样式文件
- 独立的JavaScript逻辑
- 独立的图片和资源文件
- 独立的URL结构和路由

### 域名绑定实现方案
1. **域名识别机制**
   - 通过HTTP请求头的Host字段识别域名
   - 后端维护域名与语言的映射表
   - 根据域名返回对应语言的模板和内容

2. **模板管理方案**
   - 每个语言模板存放在独立目录
   - 模板命名规范：`templates/{language_code}/`
   - 支持模板热更新和版本管理

3. **内容数据隔离**
   - 每个语言站点使用独立的数据表前缀
   - 文章、分类、评论完全隔离
   - 用户数据按语言分别存储

### 新语言扩展流程
1. 复制现有语言模板目录
2. 翻译所有静态文本内容
3. 调整本地化元素（日期格式、货币符号等）
4. 后台添加新语言配置
5. 绑定新域名到新语言
6. 测试验证功能完整性

## 📊 SEO优化要求

### Google SEO最佳实践标准
请联网搜索并整理最新的Google SEO最佳实践，重点关注：

#### URL结构优化
- 使用本地化语言的URL Slug
- URL层级不超过3层
- 包含目标关键词
- 避免特殊字符和数字ID

#### 元数据优化
- 每篇文章独立的title、description、keywords
- 标题长度控制在50-60字符
- 描述长度控制在150-160字符
- 关键词密度控制在2-3%

#### 结构化数据
- Article Schema标记
- BreadcrumbList导航标记
- Organization组织信息
- WebSite搜索功能标记

#### 技术SEO
- 页面加载速度优化
- 移动端友好性
- HTTPS安全连接
- XML Sitemap自动生成
- robots.txt优化配置

### 内容SEO策略
1. **分类设计**
   - 猫咪相关：猫咪健康、猫咪行为、猫咪品种、猫咪护理
   - 狗狗相关：狗狗健康、狗狗训练、狗狗品种、狗狗护理
   - 最多二级分类，避免过深层级

2. **内容质量要求**
   - 文章字数不少于800字
   - 原创性内容，避免重复
   - 包含相关图片和视频
   - 定期更新和维护

## 💻 开发规范与约束

### 代码质量标准
1. **命名规范**
   - 使用有意义的变量和函数名
   - 遵循驼峰命名法
   - 常量使用大写字母

2. **注释要求**
   - 所有函数必须有详细注释
   - 复杂逻辑必须有行内注释
   - API接口必须有完整文档

3. **错误处理**
   - 所有异步操作必须有错误处理
   - 用户友好的错误提示
   - 详细的错误日志记录

### 安全性要求
1. **输入验证**
   - 所有用户输入必须验证和过滤
   - SQL注入防护
   - XSS攻击防护

2. **权限控制**
   - 管理员身份验证
   - 操作权限验证
   - 敏感数据加密存储

### 性能优化要求
1. **前端优化**
   - 图片懒加载
   - CSS和JS文件压缩
   - CDN加速配置

2. **后端优化**
   - 数据库查询优化
   - Redis缓存策略
   - API响应时间监控

## 🤖 AI开发特殊指导

### 开发步骤拆分原则
**重要**：必须将开发任务拆分为不少于60个具体步骤，每个步骤遵循以下原则：

1. **单一职责原则**
   - 每个步骤只完成一个明确的功能模块
   - 避免跨模块的复杂操作
   - 确保步骤的原子性和独立性

2. **上下文控制**
   - 每个步骤的代码量控制在Claude 200K上下文的1/3以内
   - 提供充分的背景信息和依赖关系说明
   - 明确输入和输出要求

3. **依赖关系管理**
   - 明确标注每个步骤的前置依赖
   - 提供相关文档的引用链接
   - 确保步骤间的逻辑连贯性

### 文档引用规范
每个开发步骤必须包含：
1. **相关文档引用**
   - 技术架构文档引用
   - API设计文档引用
   - 数据库设计文档引用

2. **代码规范引用**
   - 编码标准文档
   - 命名规范文档
   - 注释规范文档

3. **测试要求引用**
   - 单元测试标准
   - 集成测试要求
   - 验收测试标准

### 质量控制要求
1. **代码审查清单**
   - 功能完整性检查
   - 代码规范性检查
   - 安全性检查
   - 性能检查

2. **测试验证要求**
   - 每个功能模块必须有单元测试
   - 关键流程必须有集成测试
   - 用户界面必须有端到端测试

3. **文档更新要求**
   - 代码变更必须同步更新文档
   - API变更必须更新接口文档
   - 新功能必须更新用户手册

## 🔧 环境配置与部署

### 开发环境配置
**本地开发环境（Mac系统）**：
1. **多域名本地测试方案**
   - 修改hosts文件映射测试域名
   - 使用nginx反向代理
   - 配置本地SSL证书

2. **数据库连接配置**
   - 远程MySQL连接：************
   - 数据库名：bengtai
   - 账号：bengtai
   - 密码：weizhen258

3. **AI翻译API配置**
   - API地址：https://ai.wanderintree.top
   - 密钥：sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
   - 模型：gemini-2.5-pro

### 生产环境部署
1. **服务器环境**
   - Linux + 宝塔面板
   - Node.js运行环境
   - MySQL数据库
   - Redis缓存服务

2. **域名配置**
   - 顶级域名绑定
   - SSL证书配置
   - CDN加速设置

## 📚 项目交付要求

### 必需文档清单
1. **项目总体文档**
   - 项目概述和目标
   - 技术架构设计
   - 功能需求规格书

2. **技术设计文档**
   - 数据库设计文档
   - API接口设计文档
   - 前端组件设计文档
   - 多语言实现方案

3. **开发指导文档**
   - 详细开发步骤计划（不少于60步）
   - 代码规范和标准
   - 测试验收标准

4. **运维部署文档**
   - 环境配置指南
   - 部署操作手册
   - 监控和维护指南

5. **用户使用文档**
   - 后台管理手册
   - 功能操作指南
   - 常见问题解答

### 代码交付标准
1. **完整性要求**
   - 所有功能模块完整实现
   - 包含完整的测试用例
   - 提供详细的部署脚本

2. **质量要求**
   - 代码规范性检查通过
   - 所有测试用例通过
   - 性能指标达到要求

3. **可维护性要求**
   - 代码结构清晰
   - 注释完整详细
   - 文档与代码同步

### 验收测试要求
1. **功能测试**
   - 所有页面正常访问
   - 所有功能正常工作
   - 多语言切换正常

2. **性能测试**
   - 页面加载速度达标
   - 并发访问测试通过
   - 数据库性能测试通过

3. **SEO测试**
   - Google PageSpeed测试通过
   - SEO检查工具验证通过
   - 移动端友好性测试通过

## ⚠️ 重要提醒和约束

### 开发过程中的关键注意事项
1. **严格遵循一语言一模板策略**，绝不使用i18n方案
2. **数据完全隔离**，不同语言站点数据不能混合
3. **SEO优化是第一优先级**，所有设计决策都要考虑SEO影响
4. **代码质量不能妥协**，必须有完整的测试和文档
5. **安全性是基础要求**，所有用户输入都要验证和过滤

### AI开发成功的关键因素
1. **充分理解需求**：在开始编码前，必须完全理解业务需求
2. **遵循文档规范**：严格按照设计文档进行开发
3. **保持上下文连贯**：每个步骤都要引用相关文档和前置步骤
4. **注重质量控制**：每个模块完成后都要进行测试验证
5. **及时更新文档**：代码变更要同步更新相关文档

---

**请基于以上详细需求，生成完整的项目开发文档体系和详细的开发步骤计划。确保每个文档都详细、准确、可执行，为AI开发提供完美的指导。**
